import ImageResizer, {
  ResizeFormat,
} from '@bam.tech/react-native-image-resizer';
import { readAsync } from '@lodev09/react-native-exify';
import { Platform } from 'react-native';
import RNFS from 'react-native-fs';

const IMAGE_EXTENSIONS = ['jpg', 'jpeg', 'png', 'avif', 'webp'];

const isImageType = (fileName: string) => {
  return IMAGE_EXTENSIONS.some(type =>
    fileName.toLowerCase().endsWith(`.${type}`),
  );
};

const compressImageFromUri = async (
  uri: string,
  format: ResizeFormat = 'JPEG',
  targetWidth: number = 800,
  targetHeight: number = 800,
  compressionPercentage: number = 40,
): Promise<string | null> => {
  try {
    if (!uri) {
      console.error(
        'imageUtils.ts: compressImageFromUri(): URI is undefined, or empty',
      );
      return null;
    }

    const orientation = await getExifOrientation(uri);
    const rotation =
      Platform.OS === 'ios'
        ? 0
        : mapExifOrientationToRotation(orientation ?? 1);

    const result = await ImageResizer.createResizedImage(
      uri,
      targetWidth,
      targetHeight,
      format,
      compressionPercentage,
      rotation,
      undefined,
      false,
      {
        mode: 'cover',
        onlyScaleDown: true,
      },
    );
    deleteTempFile(uri);

    return result?.uri;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageFromUri(): Failed to compress image',
      error,
    );
    return null;
  }
};

const getExifOrientation = async (
  imagePath: string,
): Promise<number | undefined> => {
  try {
    const tags = await readAsync(`file://${imagePath}`);
    return tags?.Orientation;
  } catch (error) {
    console.error(
      'imageUtils.ts: getExifOrientation(): Failed to read EXIF data',
      error,
    );
    return undefined;
  }
};

const mapExifOrientationToRotation = (orientation: number): number => {
  switch (orientation) {
    case 3:
      return 180;
    case 6:
      return 90;
    case 8:
      return 270;
    default:
      return 0;
  }
};

const removeFilesInBatch = async (
  filePaths: string[],
  batchSize: number = 10,
) => {
  for (let i = 0; i < filePaths.length; i += batchSize) {
    const batch = filePaths.slice(i, i + batchSize);
    const promises = batch.map(filePath => deleteTempFile(filePath));

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      console.error(
        'imageUtils.ts: removeFilesInBatch(): Failed to delete files in batch',
        error,
      );
      throw error;
    }
  }
};

export const getRandomImgFile = async () => {
  const imageArray = [
    // Red 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==',
    // Blue 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Green 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
    // Yellow 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Purple 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Orange 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // White 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    // Black 1x1 PNG
    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
  ];
  const randomIndex = Math.floor(Math.random() * imageArray.length);

  const randomImg = imageArray[randomIndex];

  return randomImg;
};

export const convertFileToBase64 = async (
  filePath: string,
): Promise<string | null> => {
  try {
    return await RNFS.readFile(filePath, 'base64');
  } catch (error) {
    console.error(
      error,
      'imageUtils.ts: convertFileToBase64(): Failed to read file as base64 string',
    );
    return null;
  }
};

/**
 * Calculates the Laplacian variance of an image to detect blur
 * Higher variance indicates a sharper image, lower variance indicates blur
 * @param imageData - Base64 encoded image data or ImageData object
 * @returns Promise<number> - The Laplacian variance value
 */
export const calculateLaplacianVariance = async (
  imageData: string | ImageData,
): Promise<number> => {
  try {
    let pixels: ImageData;

    if (typeof imageData === 'string') {
      // Convert base64 to ImageData
      pixels = await base64ToImageData(imageData);
    } else {
      pixels = imageData;
    }

    // Convert to grayscale
    const grayscale = convertToGrayscale(pixels);

    // Apply Laplacian kernel
    const laplacian = applyLaplacianKernel(grayscale, pixels.width, pixels.height);

    // Calculate variance
    const variance = calculateVariance(laplacian);

    return variance;
  } catch (error) {
    console.error('calculateLaplacianVariance: Error calculating variance:', error);
    throw error;
  }
};

/**
 * Converts base64 image data to ImageData object
 * @param base64Data - Base64 encoded image string
 * @returns Promise<ImageData> - ImageData object
 */
const base64ToImageData = (base64Data: string): Promise<ImageData> => {
  return new Promise((resolve, reject) => {
    try {
      // Remove data URL prefix if present
      const base64 = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

      // Create a canvas element (works in React Native with react-native-canvas)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        reject(new Error('Could not get canvas context'));
        return;
      }

      const img = new Image();
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, img.width, img.height);
        resolve(imageData);
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = `data:image/jpeg;base64,${base64}`;
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Converts ImageData to grayscale
 * @param imageData - ImageData object
 * @returns Uint8Array - Grayscale pixel data
 */
const convertToGrayscale = (imageData: ImageData): Uint8Array => {
  const { data, width, height } = imageData;
  const grayscale = new Uint8Array(width * height);

  for (let i = 0; i < data.length; i += 4) {
    // Convert RGB to grayscale using luminance formula
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const gray = Math.round(0.299 * r + 0.587 * g + 0.114 * b);
    grayscale[i / 4] = gray;
  }

  return grayscale;
};

/**
 * Applies Laplacian kernel to detect edges
 * @param grayscale - Grayscale pixel data
 * @param width - Image width
 * @param height - Image height
 * @returns Float32Array - Laplacian filtered data
 */
const applyLaplacianKernel = (
  grayscale: Uint8Array,
  width: number,
  height: number,
): Float32Array => {
  const result = new Float32Array(width * height);

  // Laplacian kernel (8-connected)
  const kernel = [
    0, -1, 0,
    -1, 4, -1,
    0, -1, 0
  ];

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let sum = 0;

      // Apply kernel
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const pixelIndex = (y + ky) * width + (x + kx);
          const kernelIndex = (ky + 1) * 3 + (kx + 1);
          sum += grayscale[pixelIndex] * kernel[kernelIndex];
        }
      }

      result[y * width + x] = sum;
    }
  }

  return result;
};

/**
 * Calculates variance of the Laplacian filtered data
 * @param laplacian - Laplacian filtered data
 * @returns number - Variance value
 */
const calculateVariance = (laplacian: Float32Array): number => {
  let sum = 0;
  let sumSquared = 0;
  let count = 0;

  for (let i = 0; i < laplacian.length; i++) {
    const value = laplacian[i];
    sum += value;
    sumSquared += value * value;
    count++;
  }

  const mean = sum / count;
  const variance = (sumSquared / count) - (mean * mean);

  return variance;
};

export const deleteTempFile = async (filePath: string) => {
  try {
    const path = filePath.includes('file://')
      ? filePath.replace('file://', '')
      : filePath;

    if (path?.length) {
      const exists = await RNFS.exists(path);
      if (exists) {
        await RNFS.unlink(path);
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteTempFile(): Failed to delete temp file',
      error,
    );
    throw error;
  }
};

const compressImageAndConvertToBase64 = async (imagePath: string) => {
  try {
    const compressedImagePath = await compressImageFromUri(imagePath);
    if (!compressedImagePath) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image',
      );
      return null;
    }
    const base64 = await convertFileToBase64(compressedImagePath);
    if (!base64) {
      console.error(
        'imageUtils.ts: compressImageAndConvertToBase64(): Failed to convert image to base64',
      );
      return null;
    }
    return base64;
  } catch (error) {
    console.error(
      'imageUtils.ts: compressImageAndConvertToBase64(): Failed to compress image and convert to base64',
      error,
    );
    return null;
  }
};

async function deleteCachedImages() {
  try {
    const exists = await RNFS.exists(RNFS.CachesDirectoryPath);
    if (!exists) {
      console.info(
        'imageUtils.ts: deleteCachedImages(): CachesDirectoryPath does not exist',
      );
      return;
    }
    const files = await RNFS.readDir(RNFS.CachesDirectoryPath);
    for (const file of files) {
      if (file.isFile() && isImageType(file.name)) {
        const filePath = [RNFS.CachesDirectoryPath, file.name].join('/');
        try {
          await RNFS.unlink(filePath);
        } catch (error) {
          console.error(
            `imageUtils.ts: deleteCachedImages(): Failed to delete file ${filePath}:`,
            error,
          );
        }
      }
    }
  } catch (error) {
    console.error(
      'imageUtils.ts: deleteCachedImages(): Failed to delete cached images',
      error,
    );
  }
}

export const ImageTitle = {
  proofOfService: 'proofOfService',
  photosTaken: 'photosTaken',
  parcel: 'parcel',
  photoEmptyLockbox: 'photoEmptyLockbox',
  photoSNOTag: 'photoSNOTag',
  photoLockboxArea: 'photoLockboxArea',
  photoPerimeter: 'photoPerimeter',
  emptyCoolerInVehicleStepExamplePhotos:
    'emptyCoolerInVehicleStepExamplePhotos',
  areaWhereCoolerIsStoredPhotos: 'areaWhereCoolerIsStoredPhotos',
  areaWhereSamplesAreHandledPhotos: 'areaWhereSamplesAreHandledPhotos',
  dropOffSignature: 'dropOffSignature',
  thermometerPhoto: 'thermometerPhoto',
  lockboxPhoto: 'lockboxPhoto',
  parcelBarcodeBypassPhoto: 'parcelBarcodeBypassPhoto',
} as const;

export const ImageTitleInfo: Record<
  keyof typeof ImageTitle,
  { title: string; multiple: boolean }
> = {
  [ImageTitle.proofOfService]: {
    title: 'proof_of_service',
    multiple: false,
  },
  [ImageTitle.photosTaken]: {
    title: 'photos_taken',
    multiple: true,
  },
  [ImageTitle.parcel]: {
    title: 'parcel',
    multiple: true,
  },
  [ImageTitle.photoEmptyLockbox]: {
    title: 'sno_empty_lockbox',
    multiple: false,
  },
  [ImageTitle.photoSNOTag]: {
    title: 'sno_tag_left',
    multiple: false,
  },
  [ImageTitle.photoLockboxArea]: {
    title: 'sno_area_around_lockbox',
    multiple: false,
  },
  [ImageTitle.photoPerimeter]: {
    title: 'perimeter_check',
    multiple: true,
  },
  [ImageTitle.emptyCoolerInVehicleStepExamplePhotos]: {
    title: 'survey_empty_cooler',
    multiple: false,
  },
  [ImageTitle.areaWhereCoolerIsStoredPhotos]: {
    title: 'survey_cooler_stored',
    multiple: false,
  },
  [ImageTitle.areaWhereSamplesAreHandledPhotos]: {
    title: 'survey_samples_handled',
    multiple: false,
  },
  [ImageTitle.dropOffSignature]: {
    title: 'dropoff_signature',
    multiple: false,
  },
  [ImageTitle.thermometerPhoto]: {
    title: 'lockbox_thermometer_photo',
    multiple: false,
  },
  [ImageTitle.lockboxPhoto]: {
    title: 'lockbox_photo',
    multiple: false,
  },
  [ImageTitle.parcelBarcodeBypassPhoto]: {
    title: 'parcel_barcode_bypass_photo',
    multiple: true,
  },
} as const;

export const filterValidImageUris = (images: any[]): string[] =>
  images
    .filter(img => img?.uri && typeof img.uri === 'string')
    .map(img => img.uri);

export {
  compressImageFromUri,
  removeFilesInBatch,
  compressImageAndConvertToBase64,
  deleteCachedImages,
};
